{"name": "Campaign Creation Notification & Escalation Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "campaign-trigger", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Campaign Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "campaign-webhook-id"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.request_type }}", "rightValue": "create_campaign", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "validate-request", "name": "Validate Request Type", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"operation": "insert", "schema": {"value": "public"}, "table": {"value": "campaign_notifications"}, "columns": {"mappingMode": "defineBelow", "value": {"campaign_id": "={{ $json.campaignID }}", "campaign_name": "={{ $json.campaignName }}", "advertiser_id": "={{ $json.advertiserID }}", "advertiser_name": "={{ $json.advertiserName }}", "workspace_id": "={{ $json.workspaceID }}", "workspace_name": "={{ $json.workspaceName }}", "advertiser_email": "={{ $json.advertiser_email }}", "admin_emails": "={{ JSON.stringify($json.travelad_admin_email) }}", "webhook_url": "={{ $json.webhookurl }}", "created_time": "={{ $json.created_time }}", "escalation_count": "0", "max_tries": "3", "status": "pending", "last_notification_sent": "={{ new Date().toISOString() }}", "is_test_mode": "={{ $json.test_mode || false }}"}}, "options": {}}, "id": "insert-campaign-db", "name": "Insert Campaign to DB", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 240], "credentials": {"postgres": {"id": "postgres-credentials-id", "name": "PostgreSQL Credentials"}}}, {"parameters": {"jsCode": "// Extract admin emails with priority\nconst adminEmails = $input.first().json.travelad_admin_email;\nconst priority1Emails = adminEmails.filter(admin => admin.priority === 1).map(admin => admin.email);\nconst priority2Emails = adminEmails.filter(admin => admin.priority === 2).map(admin => admin.email);\n\n// Get test mode configuration\nconst isTestMode = $input.first().json.test_mode || false;\nconst testEmail = '<EMAIL>';\n\n// Email configuration\nconst emailConfig = {\n  from: '<EMAIL>',\n  priority1: isTestMode ? [testEmail] : priority1Emails,\n  priority2: isTestMode ? [testEmail] : priority2Emails,\n  subject: `\"${$input.first().json.campaignName}\" created by \"${$input.first().json.advertiserName}\"`,\n  campaignData: {\n    campaignName: $input.first().json.campaignName,\n    advertiserName: $input.first().json.advertiserName,\n    createdTime: $input.first().json.created_time,\n    webhookUrl: $input.first().json.webhookurl,\n    campaignId: $input.first().json.campaignID\n  }\n};\n\nreturn [emailConfig];"}, "id": "prepare-email-data", "name": "Prepare Email Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 360]}, {"parameters": {"jsCode": "// HTML Email Template Generator\nfunction generateEmailTemplate(campaignData, isEscalation = false) {\n  const escalationText = isEscalation ? '[ESCALATION] ' : '';\n  return `\n    <!DOCTYPE html><html><head><meta charset=\"UTF-8\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><title>Campaign Notification</title><style>body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f4f4f4; }.container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }.header { background: #2c3e50; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; margin: -20px -20px 20px -20px; }.content { padding: 20px 0; }.table { width: 100%; border-collapse: collapse; margin: 20px 0; }.table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }.table th { background-color: #f8f9fa; font-weight: bold; }.button { display: inline-block; padding: 12px 24px; background: #3498db; color: white; text-decoration: none; border-radius: 4px; margin: 20px 0; }.button:hover { background: #2980b9; }.footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 12px; }.escalation-warning { background: #e74c3c; color: white; padding: 10px; border-radius: 4px; margin-bottom: 20px; }</style></head><body><div class=\"container\"><div class=\"header\"><h1>${escalationText}Campaign Creation Notification</h1></div>${isEscalation ? '<div class=\"escalation-warning\"><strong>⚠️ ESCALATION NOTICE:</strong> This campaign requires immediate attention!</div>' : ''}<div class=\"content\"><p>Hello,</p><p>A new campaign has been created and requires your attention.</p><table class=\"table\"><tr><th>Campaign Name</th><td>${campaignData.campaignName}</td></tr><tr><th>Advertiser Name</th><td>${campaignData.advertiserName}</td></tr><tr><th>Campaign Created Time</th><td>${campaignData.createdTime}</td></tr><tr><th>Campaign ID</th><td>${campaignData.campaignId}</td></tr></table><p>Please review and manage this campaign by clicking the button below:</p><a href=\"${campaignData.webhookUrl}?action=manage&campaign_id=${campaignData.campaignId}\" class=\"button\">Manage Campaign</a><p><strong>Note:</strong> This is an automated notification. Please do not reply to this email.</p></div><div class=\"footer\"><p>© 2024 MyTravelAd. All rights reserved.</p><p>If you have any questions, please contact our support team.</p></div></div></body></html>\n  `;\n}\nconst emailData = $input.first().json;\nconst htmlTemplate = generateEmailTemplate(emailData.campaignData, false);\nreturn [{...emailData, htmlTemplate: htmlTemplate}];"}, "id": "generate-email-template", "name": "Generate Email Template", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 360]}, {"parameters": {"fromEmail": "={{ $json.from }}", "toEmail": "={{ $json.priority1.join(',') }}", "subject": "={{ $json.subject }}", "emailType": "html", "message": "={{ $json.htmlTemplate }}", "options": {"allowUnauthorizedCerts": false, "replyTo": "<EMAIL>"}}, "id": "send-notification-email", "name": "Send Notification Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1120, 240], "credentials": {"smtp": {"id": "smtp-credentials-id", "name": "SMTP Credentials"}}, "onError": "continueErrorOutput"}, {"parameters": {"mode": "chooseBranch", "output": "input2"}, "id": "schedule-escalation", "name": "Schedule Escalation", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 1}]}}, "id": "escalation-cron", "name": "Escalation Cron (Every Hour)", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [240, 600]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM campaign_notifications WHERE status = 'pending' AND escalation_count < max_tries AND (last_notification_sent IS NULL OR last_notification_sent < NOW() - INTERVAL '1 hour')", "options": {}}, "id": "check-pending-campaigns", "name": "Check Pending Campaigns", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [460, 600], "credentials": {"postgres": {"id": "postgres-credentials-id", "name": "PostgreSQL Credentials"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.length }}", "rightValue": 0, "operator": {"type": "number", "operation": "gt"}}], "combinator": "and"}, "options": {}}, "id": "has-pending-campaigns", "name": "Has Pending Campaigns?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 600]}, {"parameters": {"jsCode": "// Process each pending campaign for escalation\nconst pendingCampaigns = $input.all();\nconst results = [];\nfor (const campaign of pendingCampaigns) {\n  const campaignData = campaign.json;\n  const adminEmails = JSON.parse(campaignData.admin_emails);\n  const escalationCount = parseInt(campaignData.escalation_count);\n  const isTestMode = campaignData.is_test_mode;\n  const testEmail = '<EMAIL>';\n  let targetEmails;\n  if (escalationCount === 0) {\n    targetEmails = adminEmails.filter(admin => admin.priority === 1).map(admin => admin.email);\n  } else {\n    targetEmails = adminEmails.filter(admin => admin.priority === 2).map(admin => admin.email);\n  }\n  if (isTestMode) {\n    targetEmails = [testEmail];\n  }\n  results.push({\n    ...campaignData,\n    targetEmails: targetEmails,\n    escalationCount: escalationCount + 1,\n    isEscalation: true,\n    emailSubject: `[ESCALATION] \"${campaignData.campaign_name}\" created by \"${campaignData.advertiser_name}\"`,\n    campaignDataForEmail: {\n      campaignName: campaignData.campaign_name,\n      advertiserName: campaignData.advertiser_name,\n      createdTime: campaignData.created_time,\n      webhookUrl: campaignData.webhook_url,\n      campaignId: campaignData.campaign_id\n    }\n  });\n}\nreturn results;"}, "id": "process-escalations", "name": "Process Escalations", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 600]}, {"parameters": {"jsCode": "// Generate escalation email template\nfunction generateEscalationTemplate(campaignData) {\n  return `\n    <!DOCTYPE html><html><head><meta charset=\"UTF-8\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><title>Campaign Escalation</title><style>body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f4f4f4; }.container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }.header { background: #e74c3c; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; margin: -20px -20px 20px -20px; }.content { padding: 20px 0; }.table { width: 100%; border-collapse: collapse; margin: 20px 0; }.table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }.table th { background-color: #f8f9fa; font-weight: bold; }.button { display: inline-block; padding: 12px 24px; background: #e74c3c; color: white; text-decoration: none; border-radius: 4px; margin: 20px 0; }.button:hover { background: #c0392b; }.footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 12px; }.escalation-warning { background: #ff6b6b; color: white; padding: 15px; border-radius: 4px; margin-bottom: 20px; text-align: center; }.urgency-indicator { background: #ff4757; color: white; padding: 5px 10px; border-radius: 20px; font-size: 12px; font-weight: bold; }</style></head><body><div class=\"container\"><div class=\"header\"><h1>🚨 ESCALATION REQUIRED</h1><span class=\"urgency-indicator\">URGENT ATTENTION NEEDED</span></div><div class=\"escalation-warning\"><strong>⚠️ ESCALATION NOTICE:</strong> This campaign has been pending for over 1 hour and requires immediate attention!</div><div class=\"content\"><p>Hello,</p><p>The following campaign creation requires urgent review and action:</p><table class=\"table\"><tr><th>Campaign Name</th><td>${campaignData.campaignName}</td></tr><tr><th>Advertiser Name</th><td>${campaignData.advertiserName}</td></tr><tr><th>Campaign Created Time</th><td>${campaignData.createdTime}</td></tr><tr><th>Campaign ID</th><td>${campaignData.campaignId}</td></tr></table><p><strong>Please take immediate action by clicking the button below:</strong></p><a href=\"${campaignData.webhookUrl}?action=manage&campaign_id=${campaignData.campaignId}\" class=\"button\">🔧 MANAGE CAMPAIGN NOW</a><p><strong>Note:</strong> This is an automated escalation. If this campaign has already been processed, please update the system accordingly.</p></div><div class=\"footer\"><p>© 2024 MyTravelAd. All rights reserved.</p><p>Escalation System - Automated Notification</p></div></div></body></html>\n  `;\n}\nconst escalationData = $input.first().json;\nconst htmlTemplate = generateEscalationTemplate(escalationData.campaignDataForEmail);\nreturn [{...escalationData, htmlTemplate: htmlTemplate}];"}, "id": "generate-escalation-template", "name": "Generate Escalation Template", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 600]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "={{ $json.targetEmails.join(',') }}", "subject": "={{ $json.emailSubject }}", "emailType": "html", "message": "={{ $json.htmlTemplate }}", "options": {"allowUnauthorizedCerts": false, "replyTo": "<EMAIL>"}}, "id": "send-escalation-email", "name": "Send Escalation Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1340, 600], "credentials": {"smtp": {"id": "smtp-credentials-id", "name": "SMTP Credentials"}}, "onError": "continueErrorOutput"}, {"parameters": {"operation": "update", "schema": {"value": "public"}, "table": {"value": "campaign_notifications"}, "updateKey": {"value": "campaign_id"}, "columns": {"mappingMode": "defineBelow", "value": {"escalation_count": "={{ $json.escalationCount }}", "last_notification_sent": "={{ new Date().toISOString() }}", "status": "={{ $json.escalationCount >= 3 ? 'max_tries_reached' : 'pending' }}"}}, "where": {"values": [{"column": "campaign_id", "condition": "equal", "value": "={{ $json.campaign_id }}"}]}, "options": {}}, "id": "update-escalation-count", "name": "Update Escalation Count", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [1560, 600], "credentials": {"postgres": {"id": "postgres-credentials-id", "name": "PostgreSQL Credentials"}}}, {"parameters": {"httpMethod": "POST", "path": "manage-campaign", "responseMode": "responseNode", "options": {}}, "id": "manage-webhook", "name": "Manage Campaign Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 900], "webhookId": "manage-webhook-id"}, {"parameters": {"operation": "update", "schema": {"value": "public"}, "table": {"value": "campaign_notifications"}, "updateKey": {"value": "campaign_id"}, "columns": {"mappingMode": "defineBelow", "value": {"status": "managed", "managed_time": "={{ new Date().toISOString() }}", "managed_by": "={{ $json.query.user_id || 'system' }}"}}, "where": {"values": [{"column": "campaign_id", "condition": "equal", "value": "={{ $json.query.campaign_id }}"}]}, "options": {}}, "id": "update-campaign-status", "name": "Update Campaign Status", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [460, 900], "credentials": {"postgres": {"id": "postgres-credentials-id", "name": "PostgreSQL Credentials"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\"status\": \"success\", \"message\": \"Campaign has been marked as managed\", \"campaign_id\": $json.query.campaign_id, \"timestamp\": new Date().toISOString()} }}"}, "id": "manage-response", "name": "Manage Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 900]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\"status\": \"success\", \"message\": \"Campaign notification workflow initiated\", \"campaign_id\": $json.campaignID, \"timestamp\": new Date().toISOString()} }}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\"status\": \"error\", \"message\": \"Invalid request type. Expected 'create_campaign'\", \"received\": $json.request_type, \"timestamp\": new Date().toISOString()} }}"}, "id": "error-response", "name": "Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 480]}, {"parameters": {"jsCode": "// Error handling and logging\nconst errorData = $input.first().json;\nconsole.error('Email sending failed:', {error: errorData.error, campaign_id: errorData.campaign_id, timestamp: new Date().toISOString()});\nreturn [{error: true, message: 'Email sending failed', original_error: errorData.error, campaign_id: errorData.campaign_id, retry_count: errorData.retry_count || 0}];"}, "id": "handle-email-error", "name": "<PERSON><PERSON> Email <PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 120]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.retry_count }}", "rightValue": 3, "operator": {"type": "number", "operation": "lt"}}], "combinator": "and"}, "options": {}}, "id": "should-retry", "name": "Should <PERSON><PERSON>?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1340, 120]}, {"parameters": {"amount": 30, "unit": "seconds"}, "id": "retry-delay", "name": "Retry Delay", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [1560, 120]}], "connections": {"Campaign Webhook Trigger": {"main": [[{"node": "Validate Request Type", "type": "main", "index": 0}]]}, "Validate Request Type": {"main": [[{"node": "Insert Campaign to DB", "type": "main", "index": 0}, {"node": "Prepare Email Data", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}, "Insert Campaign to DB": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}, "Prepare Email Data": {"main": [[{"node": "Generate Email Template", "type": "main", "index": 0}]]}, "Generate Email Template": {"main": [[{"node": "Send Notification Email", "type": "main", "index": 0}]]}, "Send Notification Email": {"main": [[{"node": "Schedule Escalation", "type": "main", "index": 0}]], "error": [[{"node": "<PERSON><PERSON> Email <PERSON>", "type": "main", "index": 0}]]}, "Escalation Cron (Every Hour)": {"main": [[{"node": "Check Pending Campaigns", "type": "main", "index": 0}]]}, "Check Pending Campaigns": {"main": [[{"node": "Has Pending Campaigns?", "type": "main", "index": 0}]]}, "Has Pending Campaigns?": {"main": [[{"node": "Process Escalations", "type": "main", "index": 0}]]}, "Process Escalations": {"main": [[{"node": "Generate Escalation Template", "type": "main", "index": 0}]]}, "Generate Escalation Template": {"main": [[{"node": "Send Escalation Email", "type": "main", "index": 0}]]}, "Send Escalation Email": {"main": [[{"node": "Update Escalation Count", "type": "main", "index": 0}]]}, "Manage Campaign Webhook": {"main": [[{"node": "Update Campaign Status", "type": "main", "index": 0}]]}, "Update Campaign Status": {"main": [[{"node": "Manage Response", "type": "main", "index": 0}]]}, "Handle Email Error": {"main": [[{"node": "Should <PERSON><PERSON>?", "type": "main", "index": 0}]]}, "Should Retry?": {"main": [[{"node": "Retry Delay", "type": "main", "index": 0}]]}}}